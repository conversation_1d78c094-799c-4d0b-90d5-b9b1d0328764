import React from "react";
import {
  FaFacebookF,
  FaTwitter,
  FaInstagram,
  FaLinkedinIn,
} from "react-icons/fa";

const HomePage = () => {
  return (
    <div
      style={{
        display: "flex",
        minHeight: "100vh",
        backgroundColor: "#1a3d2e",
        paddingLeft: "100px",
        paddingRight: "20px",
        paddingBottom: "80px",
      }}
    >
      <div
        style={{
          width: "10%",
          height: "calc(100vh - 70px)",
          display: "flex",
          flexDirection: "column",
          alignItems: "end",
          justifyContent: "space-between",
          paddingTop: "120px",
          paddingBottom: "80px",
          borderRight: "1px solid #fff",
        }}
      >
        <div
          style={{
           
            fontSize: "18px",
            transform: "rotate(-90deg)",
            whiteSpace: "nowrap",
            marginRight: "-20px",
            color: '#CDFF9A'
          }}
        >
          2023 Dotcreativemarket
        </div>

        <div
          style={{
            display: "flex",
            flexDirection: "column",
            alignItems: "center",
            gap: "20px",
          }}
        >
          <div
            style={{
              color: "#ffffff",
              fontSize: "18px",
              transform: "rotate(-90deg)",
              whiteSpace: "nowrap",
              marginBottom: "20px",
              color: '#CDFF9A'
            }}
          >
            FOLLOW US
          </div>
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              gap: "20px",
              alignItems: "center",
            }}
          >
            <FaFacebookF
              style={{
                color: "#ffffff",
                fontSize: "18px",
                cursor: "pointer",
                transition: "color 0.3s ease",
              }}
              onMouseEnter={(e) => (e.target.style.color = "#4267B2")}
              onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
            />
            <FaTwitter
              style={{
                color: "#ffffff",
                fontSize: "18px",
                cursor: "pointer",
                transition: "color 0.3s ease",
              }}
              onMouseEnter={(e) => (e.target.style.color = "#1DA1F2")}
              onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
            />
            <FaInstagram
              style={{
                color: "#ffffff",
                fontSize: "18px",
                cursor: "pointer",
                transition: "color 0.3s ease",
              }}
              onMouseEnter={(e) => (e.target.style.color = "#E4405F")}
              onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
            />
            <FaLinkedinIn
              style={{
                color: "#ffffff",
                fontSize: "18px",
                cursor: "pointer",
                transition: "color 0.3s ease",
              }}
              onMouseEnter={(e) => (e.target.style.color = "#0077B5")}
              onMouseLeave={(e) => (e.target.style.color = "#ffffff")}
            />
          </div>
        </div>
      </div>
      <div style={{ width: "40%" }}></div>
      <div style={{ width: "50%" }}></div>
    </div>
  );
};

export default HomePage;
